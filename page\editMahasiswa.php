<?php
include 'database.php';

$nim = isset($_GET['nim']) ? $_GET['nim'] : '';
$mahasiswa = null;
$prodiList = [];
$jurusanList = [];

if ($nim) {
    // Fetch mahasiswa
    $stmt = $conn->prepare('SELECT nim_mahasiswa, nama_mahasiswa, prodi_mahasiswa, jurusan_mahasiswa, email_mahasiswa, foto_mahasiswa FROM mahasiswa WHERE nim_mahasiswa = ?');
    $stmt->bind_param('s', $nim);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result && $row = $result->fetch_assoc()) {
        $mahasiswa = $row;
    }
    $stmt->close();
    // Fetch prodi
    $result = $conn->query('SELECT id_prodi, nama_prodi FROM prodi');
    while ($row = $result->fetch_assoc()) {
        $prodiList[] = $row;
    }
    // Fetch jurusan
    $result = $conn->query('SELECT id_jurusan, nama_jurusan FROM jurusan');
    while ($row = $result->fetch_assoc()) {
        $jurusanList[] = $row;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nim = $_POST['nim_mahasiswa'];
    $nama = $_POST['nama_mahasiswa'];
    $prodi = $_POST['prodi_mahasiswa'];
    $jurusan = $_POST['jurusan_mahasiswa'];
    $email = $_POST['email_mahasiswa'];
    $password = $_POST['password_mahasiswa'];

    // Handle file upload
    $foto_path = $mahasiswa['foto_mahasiswa']; // Keep existing photo by default

    if (isset($_FILES['foto_mahasiswa']) && $_FILES['foto_mahasiswa']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'images/mahasiswa_picture/';
        $file_extension = strtolower(pathinfo($_FILES['foto_mahasiswa']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

        if (in_array($file_extension, $allowed_extensions)) {
            $new_filename = $nim . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $new_filename;

            if (move_uploaded_file($_FILES['foto_mahasiswa']['tmp_name'], $upload_path)) {
                // Delete old photo if it's not the default
                if ($mahasiswa['foto_mahasiswa'] !== 'images/mahasiswa_picture/default.jpg' && file_exists($mahasiswa['foto_mahasiswa'])) {
                    unlink($mahasiswa['foto_mahasiswa']);
                }
                $foto_path = $upload_path;
            }
        }
    }

    // Debug: Log POST data
    error_log('FORM POST DATA: ' . print_r($_POST, true));

    // Direct update instead of CURL
    if ($password !== '') {
        $sql = 'UPDATE mahasiswa SET nama_mahasiswa=?, prodi_mahasiswa=?, jurusan_mahasiswa=?, email_mahasiswa=?, password_mahasiswa=?, foto_mahasiswa=? WHERE nim_mahasiswa=?';
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('sssssss', $nama, $prodi, $jurusan, $email, $password, $foto_path, $nim);
    } else {
        $sql = 'UPDATE mahasiswa SET nama_mahasiswa=?, prodi_mahasiswa=?, jurusan_mahasiswa=?, email_mahasiswa=?, foto_mahasiswa=? WHERE nim_mahasiswa=?';
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('ssssss', $nama, $prodi, $jurusan, $email, $foto_path, $nim);
    }

    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo "<script>alert('Data berhasil diperbarui!');window.location='index.php?page=mahasiswaPage.php';</script>";
            exit;
        } else {
            echo "<script>alert('Tidak ada perubahan data atau NIM tidak ditemukan.');</script>";
        }
    } else {
        error_log('MYSQL ERROR: ' . $stmt->error);
        echo "<script>alert('Gagal memperbarui data: " . addslashes($stmt->error) . "');</script>";
    }
    $stmt->close();
}
?>
<main class="dashboard-main">
    <div class="edit-mahasiswa-card">
        <header><h1>Edit Mahasiswa</h1></header>
        <?php if ($mahasiswa): ?>
        <form method="post" enctype="multipart/form-data" class="edit-mahasiswa-form" style="display:flex;flex-direction:column;gap:1.2em;">
            <label>NIM Mahasiswa
                <input type="text" name="nim_mahasiswa" value="<?= htmlspecialchars($mahasiswa['nim_mahasiswa']) ?>" readonly style="background:#f1f1f1;cursor:not-allowed;">
            </label>
            <label>Nama Mahasiswa
                <input type="text" name="nama_mahasiswa" value="<?= htmlspecialchars($mahasiswa['nama_mahasiswa']) ?>" required>
            </label>
            <label>Prodi Mahasiswa
                <select name="prodi_mahasiswa" required>
                    <?php foreach ($prodiList as $p): ?>
                        <option value="<?= htmlspecialchars($p['id_prodi']) ?>" <?= $mahasiswa['prodi_mahasiswa'] == $p['id_prodi'] ? 'selected' : '' ?>><?= htmlspecialchars($p['nama_prodi']) ?></option>
                    <?php endforeach; ?>
                </select>
            </label>
            <label>Jurusan Mahasiswa
                <select name="jurusan_mahasiswa" required>
                    <?php foreach ($jurusanList as $j): ?>
                        <option value="<?= htmlspecialchars($j['id_jurusan']) ?>" <?= $mahasiswa['jurusan_mahasiswa'] == $j['id_jurusan'] ? 'selected' : '' ?>><?= htmlspecialchars($j['nama_jurusan']) ?></option>
                    <?php endforeach; ?>
                </select>
            </label>
            <label>Email
                <input type="email" name="email_mahasiswa" value="<?= htmlspecialchars($mahasiswa['email_mahasiswa']) ?>" required>
            </label>
            <label>Password
                <input type="password" name="password_mahasiswa" placeholder="Kosongkan jika tidak ingin mengubah password">
            </label>

            <label>Foto Profil
                <div style="margin-bottom: 0.5rem;">
                    <img src="<?= htmlspecialchars($mahasiswa['foto_mahasiswa']) ?>" alt="Current Photo" class="profile-photo" style="width: 80px; height: 80px;">
                    <small style="display: block; color: #6c757d; margin-top: 0.5rem;">Foto saat ini</small>
                </div>
                <input type="file" name="foto_mahasiswa" accept="image/*">
                <small style="color: #6c757d; font-size: 0.8rem;">Format yang didukung: JPG, JPEG, PNG, GIF (Opsional - kosongkan jika tidak ingin mengubah)</small>
            </label>

            <button type="submit">Simpan</button>
            <a href="index.php?page=mahasiswaPage.php" class="cancel-btn">Batal</a>
        </form>
        <?php else: ?>
            <p class="error-message">Data mahasiswa tidak ditemukan.</p>
        <?php endif; ?>
    </div>
</main>

<style>
input[type="file"] {
    padding: 0.5rem;
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
    transition: border-color 0.3s ease;
}

input[type="file"]:hover {
    border-color: var(--primary-color);
}
</style>