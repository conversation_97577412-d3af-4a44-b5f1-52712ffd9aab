/* Enhanced DataTables Styling */
.dataTables_wrapper {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin-bottom: 2rem;
}

/* Table Header Styling */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_length label,
.dataTables_wrapper .dataTables_filter label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    display: inline-block;
}

.dataTables_wrapper .dataTables_filter input[type="search"] {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    margin-left: 0.5rem;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
    width: 250px;
}

.dataTables_wrapper .dataTables_filter input[type="search"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.dataTables_wrapper .dataTables_length select {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 0.4rem 0.8rem;
    margin: 0 0.5rem;
    font-size: 0.9rem;
    background-color: white;
    transition: border-color 0.3s ease;
}

.dataTables_wrapper .dataTables_length select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Table Styling */
table.dataTable {
    width: 100% !important;
    border-collapse: separate;
    border-spacing: 0;
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
}

table.dataTable thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 1rem 0.75rem;
    border: none;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    position: relative;
}

table.dataTable thead th:first-child {
    border-top-left-radius: 12px;
}

table.dataTable thead th:last-child {
    border-top-right-radius: 12px;
}

table.dataTable tbody td {
    padding: 1rem 0.75rem;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
    font-size: 0.9rem;
    color: var(--text-color);
    transition: background-color 0.2s ease;
}

table.dataTable tbody tr {
    transition: all 0.2s ease;
}

table.dataTable tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

table.dataTable tbody tr:nth-child(even) {
    background-color: #fafbfc;
}

table.dataTable tbody tr:nth-child(even):hover {
    background-color: #f0f2f5;
}

/* Action Buttons */
.btn-edit, .btn-delete, .btn-delete-prodi, .btn-delete-jurusan {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.2s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.btn-edit {
    background-color: #28a745;
    color: white;
}

.btn-edit:hover {
    background-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-delete, .btn-delete-prodi, .btn-delete-jurusan {
    background-color: #dc3545;
    color: white;
}

.btn-delete:hover, .btn-delete-prodi:hover, .btn-delete-jurusan:hover {
    background-color: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

/* Add Button Styling */
.btn-add {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    color: white;
    text-decoration: none;
}

.btn-add::before {
    content: '+';
    font-size: 1.2rem;
    font-weight: bold;
}

/* Pagination Styling */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    margin: 0 2px;
    background-color: white;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    font-weight: 600;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Info Text Styling */
.dataTables_wrapper .dataTables_info {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dataTables_wrapper .dataTables_filter input[type="search"] {
        width: 200px;
    }

    table.dataTable thead th,
    table.dataTable tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .btn-edit, .btn-delete, .btn-delete-prodi, .btn-delete-jurusan {
        width: 28px;
        height: 28px;
    }
}

/* Photo styling in tables */
.profile-photo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e9ecef;
    transition: all 0.2s ease;
}

.profile-photo:hover {
    transform: scale(1.1);
    border-color: var(--primary-color);
}

/* Table container header */
.table-container header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0 0.5rem;
}

.table-container header h2 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.5rem;
    font-weight: 600;
}