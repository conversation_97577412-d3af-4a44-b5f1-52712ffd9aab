<?php
include 'database.php';

$prodiList = [];
$jurusanList = [];
$message = '';
$messageType = '';

// Fetch prodi
$result = $conn->query('SELECT id_prodi, nama_prodi FROM prodi');
while ($row = $result->fetch_assoc()) {
    $prodiList[] = $row;
}

// Fetch jurusan
$result = $conn->query('SELECT id_jurusan, nama_jurusan FROM jurusan');
while ($row = $result->fetch_assoc()) {
    $jurusanList[] = $row;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nim = $_POST['nim_mahasiswa'];
    $nama = $_POST['nama_mahasiswa'];
    $prodi = $_POST['prodi_mahasiswa'];
    $jurusan = $_POST['jurusan_mahasiswa'];
    $email = $_POST['email_mahasiswa'];
    $password = $_POST['password_mahasiswa'];
    
    // Handle file upload
    $foto_path = 'images/mahasiswa_picture/default.jpg'; // Default photo
    
    if (isset($_FILES['foto_mahasiswa']) && $_FILES['foto_mahasiswa']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'images/mahasiswa_picture/';
        $file_extension = strtolower(pathinfo($_FILES['foto_mahasiswa']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
        
        if (in_array($file_extension, $allowed_extensions)) {
            $new_filename = $nim . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $new_filename;
            
            if (move_uploaded_file($_FILES['foto_mahasiswa']['tmp_name'], $upload_path)) {
                $foto_path = $upload_path;
            } else {
                $message = 'Gagal mengupload foto. Menggunakan foto default.';
                $messageType = 'warning';
            }
        } else {
            $message = 'Format file tidak didukung. Gunakan JPG, JPEG, PNG, atau GIF.';
            $messageType = 'error';
        }
    }
    
    // Insert into database
    if (empty($message) || $messageType === 'warning') {
        $sql = 'INSERT INTO mahasiswa (nim_mahasiswa, nama_mahasiswa, prodi_mahasiswa, jurusan_mahasiswa, email_mahasiswa, password_mahasiswa, foto_mahasiswa) VALUES (?, ?, ?, ?, ?, ?, ?)';
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('sssssss', $nim, $nama, $prodi, $jurusan, $email, $password, $foto_path);
        
        if ($stmt->execute()) {
            $message = 'Data mahasiswa berhasil ditambahkan!';
            $messageType = 'success';
            // Clear form data
            $nim = $nama = $prodi = $jurusan = $email = $password = '';
        } else {
            $message = 'Gagal menambahkan data mahasiswa: ' . $stmt->error;
            $messageType = 'error';
        }
        $stmt->close();
    }
}
?>

<main class="dashboard-main">
    <div class="edit-mahasiswa-card">
        <header><h1>Tambah Mahasiswa</h1></header>
        
        <?php if ($message): ?>
            <div class="message <?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>
        
        <form method="post" enctype="multipart/form-data" class="edit-mahasiswa-form" style="display:flex;flex-direction:column;gap:1.2em;">
            <label>NIM Mahasiswa
                <input type="text" name="nim_mahasiswa" value="<?= htmlspecialchars($nim ?? '') ?>" required>
            </label>
            
            <label>Nama Mahasiswa
                <input type="text" name="nama_mahasiswa" value="<?= htmlspecialchars($nama ?? '') ?>" required>
            </label>
            
            <label>Prodi Mahasiswa
                <select name="prodi_mahasiswa" required>
                    <option value="">Pilih Prodi</option>
                    <?php foreach ($prodiList as $p): ?>
                        <option value="<?= htmlspecialchars($p['id_prodi']) ?>" <?= (isset($prodi) && $prodi == $p['id_prodi']) ? 'selected' : '' ?>>
                            <?= htmlspecialchars($p['nama_prodi']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </label>
            
            <label>Jurusan Mahasiswa
                <select name="jurusan_mahasiswa" required>
                    <option value="">Pilih Jurusan</option>
                    <?php foreach ($jurusanList as $j): ?>
                        <option value="<?= htmlspecialchars($j['id_jurusan']) ?>" <?= (isset($jurusan) && $jurusan == $j['id_jurusan']) ? 'selected' : '' ?>>
                            <?= htmlspecialchars($j['nama_jurusan']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </label>
            
            <label>Email
                <input type="email" name="email_mahasiswa" value="<?= htmlspecialchars($email ?? '') ?>" required>
            </label>
            
            <label>Password
                <input type="password" name="password_mahasiswa" required>
            </label>
            
            <label>Foto Profil
                <input type="file" name="foto_mahasiswa" accept="image/*">
                <small style="color: #6c757d; font-size: 0.8rem;">Format yang didukung: JPG, JPEG, PNG, GIF (Opsional)</small>
            </label>
            
            <button type="submit">Tambah Mahasiswa</button>
            <a href="index.php?page=mahasiswaPage.php" class="cancel-btn">Batal</a>
        </form>
    </div>
</main>

<style>
.message {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

input[type="file"] {
    padding: 0.5rem;
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
    transition: border-color 0.3s ease;
}

input[type="file"]:hover {
    border-color: var(--primary-color);
}
</style>
